package routes

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/facades"
	"github.com/goravel/framework/support"
)

func Web() {
	facades.Route().Get("/", func(ctx http.Context) http.Response {
		return ctx.Response().View().Make("welcome.tmpl", map[string]any{
			"version": support.Version,
		})
	})

	facades.Route().Get("/hello", func(ctx http.Context) http.Response {
		return ctx.Response().<PERSON><PERSON>(http.StatusOK, http.Json{
			"Hello": "Goravel",
		})
	})

	facades.Route().Group(func(router route.Router) {
		router.Get("/", func(ctx http.Context) http.Response {
			return ctx.Response().<PERSON><PERSON>(http.StatusOK, http.Json{
				"Hello": "All User",
			})
		})

		router.Get("/{id}", func(ctx http.Context) http.Response {
			return ctx.Response().<PERSON><PERSON>(http.StatusOK, http.Json{
				"Hello": "User " + ctx.Request().Get("id"),
			})
		})
	}).Prefix("/users")
}
