package routes

import (
	"github.com/goravel/framework/contracts/http"
	"github.com/goravel/framework/contracts/route"
	"github.com/goravel/framework/facades"
	"github.com/goravel/framework/support"
)

func Web() {
	facades.Route().Get("/", func(ctx http.Context) http.Response {
		return ctx.Response().View().Make("welcome.tmpl", map[string]any{
			"version": support.Version,
		})
	})

	facades.Route().Get("/hello", func(ctx http.Context) http.Response {
		return ctx.Response().<PERSON><PERSON>(http.StatusOK, http.Json{
			"Hello": "Goravel",
		})
	})

	facades.Route().Prefix("v1").Group(func(router route.Router) {
		router.Get("group/{id}", func(ctx http.Context) http.Response {
			return ctx.Response().Success().String(ctx.Request().Query("id", "1"))
		})
	})
}
