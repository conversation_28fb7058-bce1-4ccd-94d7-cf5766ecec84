[2025-07-08 01:24:14] local.error: failed to initialize database, got error Error 1049 (42000): Unknown database 'goravel'
[Trace]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:189 [log.(*Writer).withStackTrace:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:92 [log.(*Writer).Errorf:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/logger.go:88 [gorm.(*Logger).Error:C]
/laragon8/usr/go/pkg/mod/gorm.io/gorm@v1.25.12/gorm.go:214 [gorm.Open:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:122 [gorm.BuildGorm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:78 [gorm.BuildQuery:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/orm/orm.go:51 [orm.BuildOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:41 [database.(*ServiceProvider).Register.func1:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:194 [foundation.(*Container).MakeOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:61 [database.(*ServiceProvider).Register.func2:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:247 [foundation.(*Container).MakeSchema:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:82 [database.(*ServiceProvider).registerCommands:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:75 [database.(*ServiceProvider).Boot:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:248 [foundation.(*Application).bootServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:235 [foundation.(*Application).bootConfiguredServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:57 [foundation.(*Application).Boot:C]
/laragon8/usr/go/blog/bootstrap/app.go:13 [bootstrap.Boot:C]
/laragon8/usr/go/blog/main.go:15 [main.main:C]
[2025-07-08 01:25:28] local.error: failed to initialize database, got error Error 1049 (42000): Unknown database 'goravel'
[Trace]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:189 [log.(*Writer).withStackTrace:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:92 [log.(*Writer).Errorf:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/logger.go:88 [gorm.(*Logger).Error:C]
/laragon8/usr/go/pkg/mod/gorm.io/gorm@v1.25.12/gorm.go:214 [gorm.Open:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:122 [gorm.BuildGorm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:78 [gorm.BuildQuery:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/orm/orm.go:51 [orm.BuildOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:41 [database.(*ServiceProvider).Register.func1:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:194 [foundation.(*Container).MakeOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:61 [database.(*ServiceProvider).Register.func2:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:247 [foundation.(*Container).MakeSchema:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:82 [database.(*ServiceProvider).registerCommands:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:75 [database.(*ServiceProvider).Boot:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:248 [foundation.(*Application).bootServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:235 [foundation.(*Application).bootConfiguredServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:57 [foundation.(*Application).Boot:C]
/laragon8/usr/go/blog/bootstrap/app.go:13 [bootstrap.Boot:C]
/laragon8/usr/go/blog/main.go:15 [main.main:C]
[2025-07-08 01:27:42] local.error: failed to initialize database, got error Error 1049 (42000): Unknown database 'goravel'
[Trace]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:189 [log.(*Writer).withStackTrace:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:92 [log.(*Writer).Errorf:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/logger.go:88 [gorm.(*Logger).Error:C]
/laragon8/usr/go/pkg/mod/gorm.io/gorm@v1.25.12/gorm.go:214 [gorm.Open:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:122 [gorm.BuildGorm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:78 [gorm.BuildQuery:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/orm/orm.go:51 [orm.BuildOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:41 [database.(*ServiceProvider).Register.func1:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:194 [foundation.(*Container).MakeOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:61 [database.(*ServiceProvider).Register.func2:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:247 [foundation.(*Container).MakeSchema:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:82 [database.(*ServiceProvider).registerCommands:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:75 [database.(*ServiceProvider).Boot:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:248 [foundation.(*Application).bootServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:235 [foundation.(*Application).bootConfiguredServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:57 [foundation.(*Application).Boot:C]
/laragon8/usr/go/blog/bootstrap/app.go:13 [bootstrap.Boot:C]
/laragon8/usr/go/blog/main.go:15 [main.main:C]
[2025-07-08 01:32:58] local.error: failed to initialize database, got error Error 1049 (42000): Unknown database 'goravel'
[Trace]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:189 [log.(*Writer).withStackTrace:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:92 [log.(*Writer).Errorf:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/logger.go:88 [gorm.(*Logger).Error:C]
/laragon8/usr/go/pkg/mod/gorm.io/gorm@v1.25.12/gorm.go:214 [gorm.Open:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:122 [gorm.BuildGorm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:78 [gorm.BuildQuery:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/orm/orm.go:51 [orm.BuildOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:41 [database.(*ServiceProvider).Register.func1:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:194 [foundation.(*Container).MakeOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:61 [database.(*ServiceProvider).Register.func2:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:247 [foundation.(*Container).MakeSchema:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:82 [database.(*ServiceProvider).registerCommands:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:75 [database.(*ServiceProvider).Boot:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:248 [foundation.(*Application).bootServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:235 [foundation.(*Application).bootConfiguredServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:57 [foundation.(*Application).Boot:C]
/laragon8/usr/go/blog/bootstrap/app.go:13 [bootstrap.Boot:C]
/laragon8/usr/go/blog/main.go:15 [main.main:C]
[2025-07-08 01:38:50] local.error: failed to initialize database, got error Error 1049 (42000): Unknown database 'goravel'
[Trace]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:189 [log.(*Writer).withStackTrace:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:92 [log.(*Writer).Errorf:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/logger.go:88 [gorm.(*Logger).Error:C]
/laragon8/usr/go/pkg/mod/gorm.io/gorm@v1.25.12/gorm.go:214 [gorm.Open:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:122 [gorm.BuildGorm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:78 [gorm.BuildQuery:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/orm/orm.go:51 [orm.BuildOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:41 [database.(*ServiceProvider).Register.func1:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:194 [foundation.(*Container).MakeOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:61 [database.(*ServiceProvider).Register.func2:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:247 [foundation.(*Container).MakeSchema:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:82 [database.(*ServiceProvider).registerCommands:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:75 [database.(*ServiceProvider).Boot:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:248 [foundation.(*Application).bootServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:235 [foundation.(*Application).bootConfiguredServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:57 [foundation.(*Application).Boot:C]
/laragon8/usr/go/blog/bootstrap/app.go:13 [bootstrap.Boot:C]
/laragon8/usr/go/blog/main.go:15 [main.main:C]
[2025-07-08 01:42:49] local.error: failed to initialize database, got error Error 1049 (42000): Unknown database 'goravel'
[Trace]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:189 [log.(*Writer).withStackTrace:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:92 [log.(*Writer).Errorf:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/logger.go:88 [gorm.(*Logger).Error:C]
/laragon8/usr/go/pkg/mod/gorm.io/gorm@v1.25.12/gorm.go:214 [gorm.Open:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:122 [gorm.BuildGorm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:78 [gorm.BuildQuery:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/orm/orm.go:51 [orm.BuildOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:41 [database.(*ServiceProvider).Register.func1:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:194 [foundation.(*Container).MakeOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:61 [database.(*ServiceProvider).Register.func2:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:247 [foundation.(*Container).MakeSchema:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:82 [database.(*ServiceProvider).registerCommands:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:75 [database.(*ServiceProvider).Boot:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:248 [foundation.(*Application).bootServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:235 [foundation.(*Application).bootConfiguredServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:57 [foundation.(*Application).Boot:C]
/laragon8/usr/go/blog/bootstrap/app.go:13 [bootstrap.Boot:C]
/laragon8/usr/go/blog/main.go:15 [main.main:C]
[2025-07-08 07:46:32] local.error: failed to initialize database, got error Error 1049 (42000): Unknown database 'goravel'
[Trace]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:189 [log.(*Writer).withStackTrace:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/log/logrus_writer.go:92 [log.(*Writer).Errorf:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/logger.go:88 [gorm.(*Logger).Error:C]
/laragon8/usr/go/pkg/mod/gorm.io/gorm@v1.25.12/gorm.go:214 [gorm.Open:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:122 [gorm.BuildGorm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/gorm/query.go:78 [gorm.BuildQuery:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/orm/orm.go:51 [orm.BuildOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:41 [database.(*ServiceProvider).Register.func1:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:194 [foundation.(*Container).MakeOrm:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:61 [database.(*ServiceProvider).Register.func2:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:360 [foundation.(*Container).make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:63 [foundation.(*Container).Make:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/container.go:247 [foundation.(*Container).MakeSchema:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:82 [database.(*ServiceProvider).registerCommands:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/database/service_provider.go:75 [database.(*ServiceProvider).Boot:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:248 [foundation.(*Application).bootServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:235 [foundation.(*Application).bootConfiguredServiceProviders:C]
/laragon8/usr/go/pkg/mod/github.com/goravel/framework@v1.15.9/foundation/application.go:57 [foundation.(*Application).Boot:C]
/laragon8/usr/go/blog/bootstrap/app.go:13 [bootstrap.Boot:C]
/laragon8/usr/go/blog/main.go:15 [main.main:C]
